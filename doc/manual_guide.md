# 电脑微信浏览朋友圈
## 一、浏览全部朋友圈
打开微信朋友圈页面进行浏览。缺点是只能浏览最近100天的记录。<br/>
![朋友圈页面.png](/doc/pic/朋友圈页面.png)<br/>
## 二、浏览单个朋友
* 此方法没有最大天数限制<br/>
* 打开搜一搜<br/>
![打开搜一搜.png](/doc/pic/打开搜一搜.png)<br/>
* 点击朋友圈选项卡<br/>
![打开朋友圈.png](/doc/pic/打开朋友圈.png)<br/>
* 输入数字1，点击搜索<br/>
![点击搜索.png](/doc/pic/点击搜索.png)<br/>
* 展开朋友下拉框<br/>
![展开朋友.png](/doc/pic/展开朋友.png)<br/>
* 搜索一个朋友点击完成<br/>
![点击完成.png](/doc/pic/点击完成.png)<br/>
* 输入一个**中文**的问号 ？ 再次点击搜索<br/>
![中文问号.png](/doc/pic/中文问号.png)<br/>
* 搜一搜有**最大展示数量限制**，如果展示不全，可以限定一下搜索时间<br/>
![限定时间.png](/doc/pic/限定时间.png)<br/>


## 三、提高自动化操作成功率
打开项目 resource/auto_gui文件夹，有四个图片<br/>
![四幅图.png](/doc/pic/四幅图.png)<br/>
程序根据这四个图片定位鼠标位置，进行自动化操作。<br/>
识图成功率与电脑的分辨率，缩放比例有很大关系。<br/>
将这四个图片替换成自己电脑的按钮截图，可以极大提高自动化操作成功率<br/>
# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: 打包发布

on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  pyinstaller-build:
    runs-on: windows-latest
    steps:
      - name: Create Executable
        uses: sayyid5416/pyinstaller@v1
        with:
          python_ver: '3.11'
          requirements: 'requirements.txt'
          spec: 'main.spec'
          upload_exe_with_name: 'wechat_moments'
